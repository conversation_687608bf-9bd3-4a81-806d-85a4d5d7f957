"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  XCircle,
  Clock,
  Target,
  RotateCcw,
  BookOpen
} from "lucide-react";

interface MatchingItem {
  id: string;
  leftItems: string[];
  rightItems: string[];
  correctMatches: { left: number; right: number }[];
  topic: string;
  difficulty: 'easy' | 'medium' | 'hard';
  explanation?: string;
}

interface MatchingGameProps {
  certificate: Certificate;
  topics: string[];
  repairCenterQuestions?: Array<{
    questionId: string;
    question: {
      question?: string;
      choiceA?: string;
      choiceB?: string;
      choiceC?: string;
      choiceD?: string;
      correctAnswer?: string;
      category?: string;
    };
    priority: string;
    successRate: number;
    topics: string[];
  }>;
  onBack: () => void;
}

export default function MatchingGame({
  certificate,
  topics,
  repairCenterQuestions = [],
  onBack
}: MatchingGameProps) {
  const [matchingItems, setMatchingItems] = useState<MatchingItem[]>([]);
  const [currentItemIndex, setCurrentItemIndex] = useState(0);
  const [userMatches, setUserMatches] = useState<{ left: number; right: number }[]>([]);
  const [selectedLeft, setSelectedLeft] = useState<number | null>(null);
  const [selectedRight, setSelectedRight] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showResult, setShowResult] = useState(false);
  const [sessionStartTime] = useState(Date.now());
  const [itemStartTime, setItemStartTime] = useState(Date.now());

  const { toast } = useToast();

  const generateMatchingItems = useCallback(async () => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/GenerateRepairCenterExam', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          topics,
          certificateName: certificate.name,
          gameType: 'matching',
          itemCount: 8,
          difficulty: 'medium',
          repairCenterQuestions: repairCenterQuestions // Pass the actual questions
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.items) {
          setMatchingItems(result.data.items);
        } else {
          throw new Error('Failed to generate matching items');
        }
      } else {
        throw new Error('API request failed');
      }
    } catch (error) {
      console.error('Error generating matching items:', error);
      toast({
        title: "Error",
        description: "Failed to generate matching exercises. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [topics, certificate.name, repairCenterQuestions, toast]);

  useEffect(() => {
    generateMatchingItems();
  }, [generateMatchingItems]);

  const handleLeftClick = (index: number) => {
    if (showResult) return;
    
    if (selectedLeft === index) {
      setSelectedLeft(null);
    } else {
      setSelectedLeft(index);
      if (selectedRight !== null) {
        createMatch(index, selectedRight);
      }
    }
  };

  const handleRightClick = (index: number) => {
    if (showResult) return;
    
    if (selectedRight === index) {
      setSelectedRight(null);
    } else {
      setSelectedRight(index);
      if (selectedLeft !== null) {
        createMatch(selectedLeft, index);
      }
    }
  };

  const createMatch = (leftIndex: number, rightIndex: number) => {
    // Check if either item is already matched
    const existingMatch = userMatches.find(
      match => match.left === leftIndex || match.right === rightIndex
    );
    
    if (existingMatch) {
      // Remove existing match
      setUserMatches(prev => prev.filter(
        match => match.left !== leftIndex && match.right !== rightIndex
      ));
    }
    
    // Add new match
    setUserMatches(prev => [...prev, { left: leftIndex, right: rightIndex }]);
    setSelectedLeft(null);
    setSelectedRight(null);
  };

  const handleSubmit = () => {
    const currentItem = matchingItems[currentItemIndex];
    let correctCount = 0;
    
    userMatches.forEach(userMatch => {
      const isCorrect = currentItem.correctMatches.some(
        correctMatch => correctMatch.left === userMatch.left && correctMatch.right === userMatch.right
      );
      if (isCorrect) correctCount++;
    });
    
    const accuracy = (correctCount / currentItem.correctMatches.length) * 100;
    setShowResult(true);
    
    toast({
      title: accuracy >= 80 ? "Great Job!" : accuracy >= 60 ? "Good Effort!" : "Keep Practicing!",
      description: `You got ${correctCount} out of ${currentItem.correctMatches.length} matches correct (${Math.round(accuracy)}%)`,
      variant: accuracy >= 60 ? "default" : "destructive",
    });
  };

  const handleReset = () => {
    setUserMatches([]);
    setSelectedLeft(null);
    setSelectedRight(null);
    setShowResult(false);
  };

  const handleNext = () => {
    if (currentItemIndex < matchingItems.length - 1) {
      setCurrentItemIndex(currentItemIndex + 1);
      setUserMatches([]);
      setSelectedLeft(null);
      setSelectedRight(null);
      setShowResult(false);
      setItemStartTime(Date.now());
    }
  };

  const handlePrevious = () => {
    if (currentItemIndex > 0) {
      setCurrentItemIndex(currentItemIndex - 1);
      setUserMatches([]);
      setSelectedLeft(null);
      setSelectedRight(null);
      setShowResult(false);
      setItemStartTime(Date.now());
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800 border-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'hard': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const isMatched = (side: 'left' | 'right', index: number) => {
    return userMatches.some(match => match[side] === index);
  };

  const getMatchPartner = (side: 'left' | 'right', index: number) => {
    const match = userMatches.find(match => match[side] === index);
    return match ? match[side === 'left' ? 'right' : 'left'] : null;
  };

  const isCorrectMatch = (leftIndex: number, rightIndex: number) => {
    const currentItem = matchingItems[currentItemIndex];
    return currentItem.correctMatches.some(
      match => match.left === leftIndex && match.right === rightIndex
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-600 mx-auto mb-4"></div>
          <p>Generating matching exercises...</p>
        </div>
      </div>
    );
  }

  if (matchingItems.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 flex items-center justify-center">
        <div className="text-center">
          <p className="mb-4">No matching exercises available.</p>
          <Button onClick={onBack}>Back to Repair Center</Button>
        </div>
      </div>
    );
  }

  const currentItem = matchingItems[currentItemIndex];
  const progress = ((currentItemIndex + 1) / matchingItems.length) * 100;
  const sessionTime = Math.floor((Date.now() - sessionStartTime) / 60000);

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
      <div className="max-w-6xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Repair Center
          </Button>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="flex items-center gap-1">
              <Target className="h-3 w-3" />
              {currentItemIndex + 1} of {matchingItems.length}
            </Badge>
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {sessionTime}m
            </Badge>
            <Button variant="outline" size="sm" onClick={handleReset}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </div>

        {/* Progress */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Progress</span>
            <span>{Math.round(progress)}% complete</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-orange-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Current Matching Exercise */}
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-orange-600" />
                Match the Items
              </CardTitle>
              <div className="flex items-center gap-2">
                <Badge className={getDifficultyColor(currentItem.difficulty)}>
                  {currentItem.difficulty}
                </Badge>
                <Badge variant="outline">
                  {currentItem.topic}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-center text-gray-600 dark:text-gray-400 mb-6">
              Click items from both columns to match them. Click again to unselect.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Left Column */}
              <div className="space-y-3">
                <h3 className="font-semibold text-center text-gray-900 dark:text-white">
                  Terms/Concepts
                </h3>
                {currentItem.leftItems.map((item, index) => {
                  const isSelected = selectedLeft === index;
                  const isMatchedItem = isMatched('left', index);
                  const partner = getMatchPartner('left', index);
                  const isCorrect = showResult && partner !== null && isCorrectMatch(index, partner);
                  
                  return (
                    <Button
                      key={index}
                      variant="outline"
                      onClick={() => handleLeftClick(index)}
                      className={`w-full p-4 h-auto text-left justify-start ${
                        isSelected 
                          ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20' 
                          : isMatchedItem
                            ? showResult
                              ? isCorrect
                                ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                                : 'border-red-500 bg-red-50 dark:bg-red-900/20'
                              : 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'hover:border-orange-300'
                      }`}
                      disabled={showResult}
                    >
                      <div className="flex items-center gap-2">
                        {showResult && isMatchedItem && (
                          isCorrect ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-600" />
                          )
                        )}
                        <span className="text-sm">{item}</span>
                      </div>
                    </Button>
                  );
                })}
              </div>

              {/* Right Column */}
              <div className="space-y-3">
                <h3 className="font-semibold text-center text-gray-900 dark:text-white">
                  Definitions/Examples
                </h3>
                {currentItem.rightItems.map((item, index) => {
                  const isSelected = selectedRight === index;
                  const isMatchedItem = isMatched('right', index);
                  const partner = getMatchPartner('right', index);
                  const isCorrect = showResult && partner !== null && isCorrectMatch(partner, index);
                  
                  return (
                    <Button
                      key={index}
                      variant="outline"
                      onClick={() => handleRightClick(index)}
                      className={`w-full p-4 h-auto text-left justify-start ${
                        isSelected 
                          ? 'border-orange-500 bg-orange-50 dark:bg-orange-900/20' 
                          : isMatchedItem
                            ? showResult
                              ? isCorrect
                                ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                                : 'border-red-500 bg-red-50 dark:bg-red-900/20'
                              : 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                            : 'hover:border-orange-300'
                      }`}
                      disabled={showResult}
                    >
                      <div className="flex items-center gap-2">
                        {showResult && isMatchedItem && (
                          isCorrect ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-600" />
                          )
                        )}
                        <span className="text-sm">{item}</span>
                      </div>
                    </Button>
                  );
                })}
              </div>
            </div>

            {/* Submit Button */}
            {!showResult && userMatches.length === currentItem.correctMatches.length && (
              <div className="text-center mt-6">
                <Button onClick={handleSubmit} className="bg-orange-600 hover:bg-orange-700">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Submit Matches
                </Button>
              </div>
            )}

            {/* Results */}
            {showResult && currentItem.explanation && (
              <div className="mt-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-lg p-4">
                <div className="flex items-start gap-2">
                  <BookOpen className="h-4 w-4 text-blue-600 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">Explanation:</h4>
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      {currentItem.explanation}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentItemIndex === 0}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          {currentItemIndex < matchingItems.length - 1 ? (
            <Button
              onClick={handleNext}
              disabled={!showResult}
            >
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button
              onClick={onBack}
              disabled={!showResult}
              className="bg-green-600 hover:bg-green-700"
            >
              Complete
              <CheckCircle className="h-4 w-4 ml-2" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}
