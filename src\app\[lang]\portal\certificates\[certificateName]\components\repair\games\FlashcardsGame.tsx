"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  ArrowRight,
  RotateCcw,
  CheckCircle,
  XCircle,
  Eye,
  EyeOff,
  Shuffle,
  Clock
} from "lucide-react";

interface FlashcardItem {
  id: string;
  front: string;
  back: string;
  topic: string;
  difficulty: 'easy' | 'medium' | 'hard';
  hints?: string[];
}

interface FlashcardsGameProps {
  certificate: Certificate;
  topics: string[];
  repairCenterQuestions?: Array<{
    questionId: string;
    question: {
      question?: string;
      choiceA?: string;
      choiceB?: string;
      choiceC?: string;
      choiceD?: string;
      correctAnswer?: string;
      category?: string;
    };
    priority: string;
    successRate: number;
    topics: string[];
  }>;
  onBack: () => void;
}

export default function FlashcardsGame({
  certificate,
  topics,
  repairCenterQuestions = [],
  onBack
}: FlashcardsGameProps) {
  const [flashcards, setFlashcards] = useState<FlashcardItem[]>([]);
  const [currentCardIndex, setCurrentCardIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [knownCards, setKnownCards] = useState<Set<number>>(new Set());
  const [unknownCards, setUnknownCards] = useState<Set<number>>(new Set());
  const [showHints, setShowHints] = useState(false);
  const [sessionStartTime] = useState(Date.now());
  const [cardStartTime, setCardStartTime] = useState(Date.now());

  const { toast } = useToast();

  const generateFlashcards = useCallback(async () => {
    try {
      setIsLoading(true);

      const response = await fetch('/api/GenerateRepairCenterExam', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          topics,
          certificateName: certificate.name,
          gameType: 'flashcards',
          itemCount: 15,
          difficulty: 'medium',
          repairCenterQuestions: repairCenterQuestions // Pass the actual questions
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.items) {
          setFlashcards(result.data.items);
        } else {
          throw new Error('Failed to generate flashcards');
        }
      } else {
        throw new Error('API request failed');
      }
    } catch (error) {
      console.error('Error generating flashcards:', error);
      toast({
        title: "Error",
        description: "Failed to generate flashcards. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [topics, certificate.name, repairCenterQuestions, toast]);

  useEffect(() => {
    generateFlashcards();
  }, [generateFlashcards]);

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  const handleKnown = () => {
    setKnownCards(prev => new Set(prev.add(currentCardIndex)));
    handleNextCard();
  };

  const handleUnknown = () => {
    setUnknownCards(prev => new Set(prev.add(currentCardIndex)));
    handleNextCard();
  };

  const handleNextCard = () => {
    if (currentCardIndex < flashcards.length - 1) {
      setCurrentCardIndex(currentCardIndex + 1);
      setIsFlipped(false);
      setShowHints(false);
      setCardStartTime(Date.now());
    }
  };

  const handlePreviousCard = () => {
    if (currentCardIndex > 0) {
      setCurrentCardIndex(currentCardIndex - 1);
      setIsFlipped(false);
      setShowHints(false);
      setCardStartTime(Date.now());
    }
  };

  const handleShuffle = () => {
    const shuffled = [...flashcards].sort(() => Math.random() - 0.5);
    setFlashcards(shuffled);
    setCurrentCardIndex(0);
    setIsFlipped(false);
    setShowHints(false);
    setKnownCards(new Set());
    setUnknownCards(new Set());
  };

  const handleRestart = () => {
    setCurrentCardIndex(0);
    setIsFlipped(false);
    setShowHints(false);
    setKnownCards(new Set());
    setUnknownCards(new Set());
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800 border-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'hard': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p>Generating flashcards...</p>
        </div>
      </div>
    );
  }

  if (flashcards.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 flex items-center justify-center">
        <div className="text-center">
          <p className="mb-4">No flashcards available.</p>
          <Button onClick={onBack}>Back to Repair Center</Button>
        </div>
      </div>
    );
  }

  const currentCard = flashcards[currentCardIndex];
  const progress = ((currentCardIndex + 1) / flashcards.length) * 100;
  const sessionTime = Math.floor((Date.now() - sessionStartTime) / 60000);

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20">
      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button variant="ghost" onClick={onBack} className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Repair Center
          </Button>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              {sessionTime}m
            </Badge>
            <Button variant="outline" size="sm" onClick={handleShuffle}>
              <Shuffle className="h-4 w-4 mr-2" />
              Shuffle
            </Button>
            <Button variant="outline" size="sm" onClick={handleRestart}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Restart
            </Button>
          </div>
        </div>

        {/* Progress */}
        <div className="mb-6">
          <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
            <span>Card {currentCardIndex + 1} of {flashcards.length}</span>
            <span>Known: {knownCards.size} | Unknown: {unknownCards.size}</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Flashcard */}
        <div className="mb-6">
          <Card 
            className={`min-h-[400px] cursor-pointer transition-all duration-300 transform hover:scale-105 ${
              isFlipped ? 'bg-blue-50 dark:bg-blue-900/20' : 'bg-white dark:bg-gray-800'
            }`}
            onClick={handleFlip}
          >
            <CardContent className="p-8 flex flex-col justify-center items-center text-center min-h-[400px]">
              <div className="mb-4 flex items-center gap-2">
                <Badge className={getDifficultyColor(currentCard.difficulty)}>
                  {currentCard.difficulty}
                </Badge>
                <Badge variant="outline">
                  {currentCard.topic}
                </Badge>
              </div>

              <div className="flex-1 flex items-center justify-center">
                <div className="max-w-2xl">
                  {!isFlipped ? (
                    <div>
                      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                        {currentCard.front}
                      </h2>
                      <div className="flex items-center justify-center gap-2 text-gray-500 dark:text-gray-400">
                        <Eye className="h-4 w-4" />
                        <span className="text-sm">Click to reveal answer</span>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <h2 className="text-xl font-bold text-blue-900 dark:text-blue-100 mb-4">
                        {currentCard.back}
                      </h2>
                      <div className="flex items-center justify-center gap-2 text-blue-600 dark:text-blue-400">
                        <EyeOff className="h-4 w-4" />
                        <span className="text-sm">Click to hide answer</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Hints */}
              {currentCard.hints && currentCard.hints.length > 0 && !isFlipped && (
                <div className="mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowHints(!showHints);
                    }}
                  >
                    {showHints ? 'Hide Hints' : 'Show Hints'}
                  </Button>
                  {showHints && (
                    <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                      {currentCard.hints.map((hint, index) => (
                        <div key={index} className="mb-1">💡 {hint}</div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Controls */}
        <div className="flex justify-between items-center">
          <Button
            variant="outline"
            onClick={handlePreviousCard}
            disabled={currentCardIndex === 0}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Previous
          </Button>

          {isFlipped && (
            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={handleUnknown}
                className="flex items-center gap-2 hover:bg-red-50 hover:border-red-300"
              >
                <XCircle className="h-4 w-4 text-red-600" />
                Need Review
              </Button>
              <Button
                onClick={handleKnown}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                <CheckCircle className="h-4 w-4" />
                Got It!
              </Button>
            </div>
          )}

          <Button
            variant="outline"
            onClick={handleNextCard}
            disabled={currentCardIndex === flashcards.length - 1}
          >
            Next
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>

        {/* Session Summary */}
        {currentCardIndex === flashcards.length - 1 && (knownCards.size + unknownCards.size) === flashcards.length && (
          <Card className="mt-6 bg-green-50 dark:bg-green-900/20 border-green-200">
            <CardContent className="p-6 text-center">
              <h3 className="text-lg font-bold text-green-900 dark:text-green-100 mb-2">
                Session Complete! 🎉
              </h3>
              <div className="text-sm text-green-700 dark:text-green-300">
                <p>Known: {knownCards.size} cards</p>
                <p>Need Review: {unknownCards.size} cards</p>
                <p>Session Time: {sessionTime} minutes</p>
              </div>
              <div className="mt-4 flex gap-2 justify-center">
                <Button onClick={handleRestart} variant="outline">
                  Study Again
                </Button>
                <Button onClick={onBack}>
                  Back to Repair Center
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
